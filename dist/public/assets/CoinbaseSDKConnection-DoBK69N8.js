import{aH as t,aI as S,dP as g}from"./index-pmk3GmSA.js";function C(e){const{onBack:l,done:a,wallet:c,walletInfo:s,onGetStarted:u,locale:n}=e,[f,o]=t.useState(!1),r=t.useCallback(()=>{o(!1),c.connect({client:e.client,chain:e.chain}).then(()=>{a()}).catch(d=>{console.error(d),o(!0)})},[e.client,c,e.chain,a]),i=t.useRef(!1);return t.useEffect(()=>{i.current||(i.current=!0,r())},[r]),S.jsx(g,{locale:{getStartedLink:n.getStartedLink,instruction:n.connectionScreen.instruction,tryAgain:n.connectionScreen.retry,inProgress:n.connectionScreen.inProgress,failed:n.connectionScreen.failed},onBack:l,walletName:s.name,walletId:c.id,errorConnecting:f,onRetry:r,onGetStarted:u,client:e.client,size:e.size})}export{C as default};
