const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/biconomy-CLjVfNKM.js","assets/index-CLtqW0Fd.js","assets/index-fOyOxnwC.css","assets/openzeppelin-BFEepaJF.js","assets/engine-Bs_3YtkS.js"])))=>i.map(i=>d[i]);
import{_ as p,at as d}from"./index-CLtqW0Fd.js";async function s({account:e,transaction:i,serializableTransaction:a,gasless:n}){if(a.value&&a.value>0n)throw new Error("Gasless transactions cannot have a value");let r;if(n.provider==="biconomy"){const{relayBiconomyTransaction:t}=await p(async()=>{const{relayBiconomyTransaction:o}=await import("./biconomy-CLjVfNKM.js");return{relayBiconomyTransaction:o}},__vite__mapDeps([0,1,2]));r=await t({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="openzeppelin"){const{relayOpenZeppelinTransaction:t}=await p(async()=>{const{relayOpenZeppelinTransaction:o}=await import("./openzeppelin-BFEepaJF.js");return{relayOpenZeppelinTransaction:o}},__vite__mapDeps([3,1,2]));r=await t({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="engine"){const{relayEngineTransaction:t}=await p(async()=>{const{relayEngineTransaction:o}=await import("./engine-Bs_3YtkS.js");return{relayEngineTransaction:o}},__vite__mapDeps([4,1,2]));r=await t({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(!r)throw new Error("Unsupported gasless provider");return d({address:e.address,transactionHash:r.transactionHash,chainId:i.chain.id}),r}export{s as sendGaslessTransaction};
