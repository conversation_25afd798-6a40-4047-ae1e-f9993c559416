const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-pmk3GmSA.js","assets/index-BUaFfTib.css"])))=>i.map(i=>d[i]);
import{dU as c,_ as s,al as d}from"./index-pmk3GmSA.js";function l(a){return!!a.startsWith("data:application/json;base64")}function u(a){const[,r]=a.split(",");return c(r)}async function h(a){const{client:r,tokenId:n,tokenUri:e}=a;if(l(e))try{return JSON.parse(u(e))}catch(t){throw console.error("Failed to fetch base64 encoded NFT",{tokenId:n,tokenUri:e},t),t}const{download:o}=await s(async()=>{const{download:t}=await import("./index-pmk3GmSA.js").then(i=>i.eI);return{download:t}},__vite__mapDeps([0,1]));try{if(!e.includes("{id}"))return await(await o({client:r,uri:e})).json()}catch(t){throw console.error("Failed to fetch non-dynamic NFT",{tokenId:n,tokenUri:e},t),t}try{try{return await(await o({client:r,uri:e.replace("{id}",d(n,{size:32}).slice(2))})).json()}catch{return await(await o({client:r,uri:e.replace("{id}",n.toString())})).json()}}catch(t){throw console.error("Failed to fetch dynamic NFT",{tokenId:n,tokenUri:e},t),t}}export{h as f};
