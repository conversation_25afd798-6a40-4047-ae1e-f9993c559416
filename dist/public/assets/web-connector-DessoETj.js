const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/eth_getTransactionCount-TCjT1hfZ.js","assets/index-CLtqW0Fd.js","assets/index-fOyOxnwC.css"])))=>i.map(i=>d[i]);
import{X as p,k as d,f as h,Y as P,$ as L,a as G,a0 as W,M as f,a1 as v,N as T,O as $,a2 as Q,U as _,A,_ as F,b as q,a3 as b,a4 as S,a5 as x,a6 as H,a7 as C,a8 as V,a9 as J}from"./index-CLtqW0Fd.js";import{signLoginPayload as Y}from"./sign-login-payload-BS5pcbMU.js";import{e as U}from"./eth_sendRawTransaction-DPdnXbFR.js";const I=new Map,X={getItem:async n=>I.get(n)??null,setItem:async(n,e)=>{I.set(n,e)},removeItem:async n=>{I.delete(n)}};async function E({authToken:n,client:e,ecosystem:t}){const s=await d(e,t)(`${p("inAppWallet")}/api/2024-05-05/accounts`,{method:"GET",headers:{"Content-Type":"application/json","x-thirdweb-client-id":e.clientId,Authorization:`Bearer embedded-wallet-token:${n}`}});if(!s.ok){const o=await s.text().catch(()=>"Unknown error");throw new Error(`Failed to get user info: ${o}`)}return await s.json()}const K=p("inAppWallet"),Z=`${K}/`,N=`${Z}api/2023-10-20`,ee=`${N}/embedded-wallet/validate-custom-jwt`,te=`${N}/embedded-wallet/validate-custom-auth-endpoint`,B=(n,e)=>e instanceof Error?`${n}: ${e.message}`:`${n}: ${h(e)}`;async function ae(n){const t=await d(n.client,n.ecosystem)(te,{method:"POST",headers:{"Content-Type":"application/json"},body:h({payload:n.payload,developerClientId:n.client.clientId})});if(!t.ok){const a=await t.json();throw new Error(`Custom auth endpoint authentication error: ${a.message}`)}try{const{verifiedToken:a}=await t.json();return{storedToken:a}}catch(a){throw new Error(B("Malformed response from post auth_endpoint authentication",a))}}async function ne(n){const e=d(n.client,n.ecosystem),t=P({authOption:"backend",client:n.client,ecosystem:n.ecosystem}),a=await e(`${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:h({walletSecret:n.walletSecret})});if(!a.ok)throw new Error("Failed to generate backend account");return await a.json()}async function ie(n){const e=new L({storage:n.storage,clientId:n.client.clientId,ecosystem:n.ecosystem});let t=await e.getGuestSessionId();t||(t=G(32),e.saveGuestSessionId(t));const a=d(n.client,n.ecosystem),s=W({authOption:"guest",client:n.client,ecosystem:n.ecosystem}),o=await a(`${s}`,{method:"POST",headers:{"Content-Type":"application/json"},body:h({sessionId:t})});if(!o.ok)throw new Error("Failed to generate guest account");return await o.json()}async function re(n){const t=await d(n.client,n.ecosystem)(ee,{method:"POST",headers:{"Content-Type":"application/json"},body:h({jwt:n.jwt,developerClientId:n.client.clientId})});if(!t.ok){const a=await t.json();throw new Error(`JWT authentication error: ${a.message}`)}try{const{verifiedToken:a}=await t.json();return{storedToken:a}}catch(a){throw new Error(B("Malformed response from post jwt authentication",a))}}async function se({client:n,ecosystem:e,tokenToLink:t,storage:a}){const s=d(n,e),o=p("inAppWallet"),i=await a.getAuthCookie();if(!i)throw new Error("Failed to link account, no user logged in");const r={Authorization:`Bearer iaw-auth-token:${i}`,"Content-Type":"application/json"},l=await s(`${o}/api/2024-05-05/account/connect`,{method:"POST",headers:r,body:h({accountAuthTokenToConnect:t})});if(!l.ok){const u=await l.json();throw new Error(u.message||"Failed to link account.")}const{linkedAccounts:c}=await l.json();return c??[]}async function oe({client:n,ecosystem:e,profileToUnlink:t,storage:a}){const s=d(n,e),o=p("inAppWallet"),i=await a.getAuthCookie();if(!i)throw new Error("Failed to unlink account, no user logged in");const r={Authorization:`Bearer iaw-auth-token:${i}`,"Content-Type":"application/json"},l=await s(`${o}/api/2024-05-05/account/disconnect`,{method:"POST",headers:r,body:h(t)});if(!l.ok){const u=await l.json();throw new Error(u.message||"Failed to unlink account.")}const{linkedAccounts:c}=await l.json();return c??[]}async function ce({client:n,ecosystem:e,storage:t}){const a=d(n,e),s=p("inAppWallet"),o=await t.getAuthCookie();if(!o)throw new Error("Failed to get linked accounts, no user logged in");const i={Authorization:`Bearer iaw-auth-token:${o}`,"Content-Type":"application/json"},r=await a(`${s}/api/2024-05-05/accounts`,{method:"GET",headers:i});if(!r.ok){const c=await r.json();throw new Error(c.message||"Failed to get linked accounts.")}const{linkedAccounts:l}=await r.json();return l??[]}function z(){return`${p("inAppWallet")}/api/2024-05-05/login/passkey/callback`}function M(n,e){return`${p("inAppWallet")}/api/2024-05-05/login/passkey?type=${n}${e?`&username=${e}`:""}`}async function le(n){var u,w,g;if(!n.passkeyClient.isAvailable())throw new Error("Passkeys are not available on this device");const e=d(n.client,n.ecosystem),t=n.username??de(n.ecosystem),s=await(await e(M("sign-up",t))).json();if(!s.challenge)throw new Error("No challenge received");const o=s.challenge,i=await n.passkeyClient.register({name:t,challenge:o,rp:n.rp}),r={};(u=n.ecosystem)!=null&&u.partnerId&&(r["x-ecosystem-partner-id"]=n.ecosystem.partnerId),(w=n.ecosystem)!=null&&w.id&&(r["x-ecosystem-id"]=n.ecosystem.id);const c=await(await e(z(),{method:"POST",headers:{"Content-Type":"application/json",...r},body:h({type:"sign-up",authenticatorData:i.authenticatorData,credentialId:i.credentialId,serverVerificationId:s.serverVerificationId,clientData:i.clientData,username:t,credential:{publicKey:i.credential.publicKey,algorithm:i.credential.algorithm},origin:i.origin,rpId:n.rp.id})})).json();if(!c||!c.storedToken)throw new Error(`Error verifying passkey: ${c.message??"unknown error"}`);return await((g=n.storage)==null?void 0:g.savePasskeyCredentialId(i.credentialId)),c}async function ue(n){var c,u,w,g;if(!n.passkeyClient.isAvailable())throw new Error("Passkeys are not available on this device");const e=d(n.client,n.ecosystem),[t,a]=await Promise.all([e(M("sign-in")).then(y=>y.json()),(c=n.storage)==null?void 0:c.getPasskeyCredentialId()]);if(!t.challenge)throw new Error("No challenge received");const s=t.challenge,o=await n.passkeyClient.authenticate({credentialId:a??void 0,challenge:s,rp:n.rp}),i={};(u=n.ecosystem)!=null&&u.partnerId&&(i["x-ecosystem-partner-id"]=n.ecosystem.partnerId),(w=n.ecosystem)!=null&&w.id&&(i["x-ecosystem-id"]=n.ecosystem.id);const l=await(await e(z(),{method:"POST",headers:{"Content-Type":"application/json",...i},body:h({type:"sign-in",authenticatorData:o.authenticatorData,credentialId:o.credentialId,serverVerificationId:t.serverVerificationId,clientData:o.clientData,signature:o.signature,origin:o.origin,rpId:n.rp.id})})).json();if(!l||!l.storedToken)throw new Error(`Error verifying passkey: ${l.message??"unknown error"}`);return await((g=n.storage)==null?void 0:g.savePasskeyCredentialId(o.credentialId)),l}function de(n){return`${(n==null?void 0:n.id)??"wallet"}-${new Date().toISOString()}`}async function he(n){const{wallet:e,chain:t,client:a,ecosystem:s}=n,o=e.getAccount()||await e.connect({client:a,chain:t}),i=d(a,s),r=await(async()=>{const u=P({authOption:"wallet",client:n.client,ecosystem:n.ecosystem}),w=await i(`${u}&address=${o.address}&chainId=${t.id}`);if(!w.ok)throw new Error("Failed to generate SIWE login payload");return await w.json()})(),{signature:l}=await Y({payload:r,account:o});return await(async()=>{const u=W({authOption:"wallet",client:n.client,ecosystem:n.ecosystem}),w=await i(`${u}&signature=${l}&payload=${encodeURIComponent(r)}`,{method:"POST",headers:{"Content-Type":"application/json"},body:h({signature:l,payload:r})});if(!w.ok)throw new Error("Failed to verify SIWE signature");return await w.json()})()}async function we({client:n,payload:e,storage:t}){const a=await t.getAuthCookie(),s=t.ecosystem,o=d(n,s);if(!a)throw new Error("No auth token found when signing message");const i={address:e.address,chainId:e.chainId,nonce:Number(e.nonce)},r=await o(`${p("inAppWallet")}/api/v1/enclave-wallet/sign-authorization`,{method:"POST",headers:{"Content-Type":"application/json","x-thirdweb-client-id":n.clientId,Authorization:`Bearer embedded-wallet-token:${a}`},body:h(i)});if(!r.ok)throw new Error(`Failed to sign message - ${r.status} ${r.statusText}`);return await r.json()}async function pe({client:n,payload:{message:e,isRaw:t,originalMessage:a,chainId:s},storage:o}){const i=await o.getAuthCookie(),r=o.ecosystem,l=d(n,r);if(!i)throw new Error("No auth token found when signing message");const c=await l(`${p("inAppWallet")}/api/v1/enclave-wallet/sign-message`,{method:"POST",headers:{"Content-Type":"application/json","x-thirdweb-client-id":n.clientId,Authorization:`Bearer embedded-wallet-token:${i}`},body:h({messagePayload:{message:e,isRaw:t,originalMessage:a,chainId:s}})});if(!c.ok)throw new Error(`Failed to sign message - ${c.status} ${c.statusText}`);return await c.json()}async function ge({client:n,payload:e,storage:t}){const a=await t.getAuthCookie(),s=t.ecosystem,o=d(n,s);if(!a)throw new Error("No auth token found when signing transaction");const i=await o(`${p("inAppWallet")}/api/v1/enclave-wallet/sign-transaction`,{method:"POST",headers:{"Content-Type":"application/json","x-thirdweb-client-id":n.clientId,Authorization:`Bearer embedded-wallet-token:${a}`},body:h({transactionPayload:e})});if(!i.ok)throw new Error(`Failed to sign transaction - ${i.status} ${i.statusText}`);return(await i.json()).signature}async function me({client:n,payload:e,storage:t}){const a=await t.getAuthCookie(),s=t.ecosystem,o=d(n,s);if(!a)throw new Error("No auth token found when signing typed data");const i=await o(`${p("inAppWallet")}/api/v1/enclave-wallet/sign-typed-data`,{method:"POST",headers:{"Content-Type":"application/json","x-thirdweb-client-id":n.clientId,Authorization:`Bearer embedded-wallet-token:${a}`},body:h({...e})});if(!i.ok)throw new Error(`Failed to sign typed data - ${i.status} ${i.statusText}`);return await i.json()}class ye{constructor({client:e,ecosystem:t,address:a,storage:s}){Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ecosystem",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"address",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"localStorage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.client=e,this.ecosystem=t,this.address=a,this.localStorage=s}async postWalletSetUp(e){await this.localStorage.saveAuthCookie(e.storedToken.cookieString)}async getUserWalletStatus(){var o,i;const e=await this.localStorage.getAuthCookie();if(!e)return{status:"Logged Out"};const t=await E({authToken:e,client:this.client,ecosystem:this.ecosystem});if(!t)return{status:"Logged Out"};const a=t.wallets[0],s={email:(o=t.linkedAccounts.find(r=>r.details.email!==void 0))==null?void 0:o.details.email,phoneNumber:(i=t.linkedAccounts.find(r=>r.details.phone!==void 0))==null?void 0:i.details.phone,userWalletId:t.id||"",recoveryShareManagement:"ENCLAVE"};return a?{status:"Logged In, Wallet Initialized",walletAddress:a.address,authDetails:s,account:await this.getAccount()}:{status:"Logged In, Wallet Uninitialized",authDetails:s}}async getAccount(){const e=this.client,t=this.localStorage,a=this.address,s=this.ecosystem,o=async i=>{const r=v({client:e,chain:T(i.chainId)}),l={to:i.to?f(i.to):void 0,data:i.data,value:m(i.value),gas:m(i.gas),nonce:m(i.nonce)||A(await F(async()=>{const{eth_getTransactionCount:c}=await import("./eth_getTransactionCount-TCjT1hfZ.js");return{eth_getTransactionCount:c}},__vite__mapDeps([0,1,2])).then(({eth_getTransactionCount:c})=>c(r,{address:f(this.address),blockTag:"pending"}))),chainId:A(i.chainId)};return i.authorizationList&&i.authorizationList.length>0?(l.type=4,l.authorizationList=i.authorizationList,l.maxFeePerGas=m(i.maxFeePerGas),l.maxPriorityFeePerGas=m(i.maxPriorityFeePerGas)):m(i.maxFeePerGas)?(l.maxFeePerGas=m(i.maxFeePerGas),l.maxPriorityFeePerGas=m(i.maxPriorityFeePerGas),l.type=2):(l.gasPrice=m(i.gasPrice),l.type=0),ge({client:e,storage:t,payload:l})};return{address:f(a),async signTransaction(i){if(!i.chainId)throw new Error("chainId required in tx to sign");return o({chainId:i.chainId,...i})},async sendTransaction(i){const r=v({client:e,chain:T(i.chainId)}),l=await o(i),c=await U(r,l);return $({client:e,ecosystem:s,chainId:i.chainId,walletAddress:a,walletType:"inApp",transactionHash:c,contractAddress:i.to??void 0,gasPrice:i.gasPrice}),{transactionHash:c}},async signMessage({message:i,originalMessage:r,chainId:l}){const c=typeof i=="string"?{message:i,isRaw:!1,originalMessage:r,chainId:l}:{message:typeof i.raw=="string"?i.raw:Q(i.raw),isRaw:!0,originalMessage:r,chainId:l},{signature:u}=await pe({client:e,payload:c,storage:t});return u},async signTypedData(i){const r=_(i),{signature:l}=await me({client:e,payload:r,storage:t});return l},async signAuthorization(i){const r=await we({client:e,payload:i,storage:t});return{address:f(r.address),chainId:Number.parseInt(r.chainId),nonce:BigInt(r.nonce),r:BigInt(r.r),s:BigInt(r.s),yParity:Number.parseInt(r.yParity)}}}}}function m(n){return n===void 0||q(n)?n:A(n)}const fe={height:"100%",width:"100%",border:"none",backgroundColor:"transparent",colorScheme:"light",position:"fixed",top:"0px",right:"0px",zIndex:"2147483646",display:"none",pointerEvents:"all"},k=new Map;class be{constructor({link:e,baseUrl:t,iframeId:a,container:s,onIframeInitialize:o,localStorage:i,clientId:r,ecosystem:l}){if(Object.defineProperty(this,"iframe",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"POLLING_INTERVAL_SECONDS",{enumerable:!0,configurable:!0,writable:!0,value:1.4}),Object.defineProperty(this,"iframeBaseUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"localStorage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"clientId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ecosystem",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.localStorage=i,this.clientId=r,this.ecosystem=l,this.iframeBaseUrl=t,typeof document>"u")return;s=s??document.body;let c=document.getElementById(a);const u=new URL(e);if(!c||c.src!==u.href){c=document.createElement("iframe");const w={...fe};Object.assign(c.style,w),c.setAttribute("id",a),c.setAttribute("fetchpriority","high"),s.appendChild(c),c.src=u.href;const g=y=>{if(y.data.eventType==="ewsIframeLoaded"){if(window.removeEventListener("message",g),!c){console.warn("thirdweb iFrame not found");return}this.onIframeLoadHandler(c,o)()}};window.addEventListener("message",g)}this.iframe=c}async onIframeLoadedInitVariables(){var e,t;return{authCookie:await this.localStorage.getAuthCookie(),deviceShareStored:await this.localStorage.getDeviceShare(),walletUserId:await this.localStorage.getWalletUserId(),clientId:this.clientId,partnerId:(e=this.ecosystem)==null?void 0:e.partnerId,ecosystemId:(t=this.ecosystem)==null?void 0:t.id}}onIframeLoadHandler(e,t){return async()=>{var o;const a=new MessageChannel,s=new Promise((i,r)=>{a.port1.onmessage=l=>{const{data:c}=l;a.port1.close(),c.success||r(new Error(c.error)),k.set(e.src,!0),t&&t(),i(!0)}});(o=e==null?void 0:e.contentWindow)==null||o.postMessage({eventType:"initIframe",data:await this.onIframeLoadedInitVariables()},this.iframeBaseUrl,[a.port2]),await s}}async call({procedureName:e,params:t,showIframe:a=!1}){var i;if(!this.iframe)throw new Error("Iframe not found. You are likely calling this from the backend where the DOM is not available.");for(;!k.get(this.iframe.src);)await b(this.POLLING_INTERVAL_SECONDS*1e3);a&&(this.iframe.style.display="block",await b(.005*1e3));const s=new MessageChannel,o=new Promise((r,l)=>{s.port1.onmessage=async c=>{const{data:u}=c;s.port1.close(),a&&(await b(.1*1e3),this.iframe&&(this.iframe.style.display="none")),u.success?r(u.data):l(new Error(u.error))}});return(i=this.iframe.contentWindow)==null||i.postMessage({eventType:e,data:{...t,...await this.onIframeLoadedInitVariables()}},this.iframeBaseUrl,[s.port2]),o}destroy(){this.iframe&&k.delete(this.iframe.src)}}class Ie extends be{constructor({clientId:e,baseUrl:t,ecosystem:a}){super({iframeId:ve+((a==null?void 0:a.id)||""),link:ke({clientId:e,path:x,ecosystem:a,baseUrl:t}).href,baseUrl:t,container:typeof document>"u"?void 0:document.body,localStorage:new L({storage:S,clientId:e,ecosystem:a}),clientId:e,ecosystem:a}),this.clientId=e,this.ecosystem=a}}function ke({clientId:n,baseUrl:e,path:t,ecosystem:a,queryParams:s}){var i;const o=new URL(`${t}`,e);if(s)for(const r of Object.keys(s))o.searchParams.set(r,((i=s[r])==null?void 0:i.toString())||"");return o.searchParams.set("clientId",n),(a==null?void 0:a.partnerId)!==void 0&&o.searchParams.set("partnerId",a.partnerId),(a==null?void 0:a.id)!==void 0&&o.searchParams.set("ecosystemId",a.id),o}const ve="thirdweb-in-app-wallet-iframe";async function Te({client:n,ecosystem:e,authToken:t}){const s=await d(n,e)(`${p("inAppWallet")}/api/v1/enclave-wallet/generate`,{method:"POST",headers:{"Content-Type":"application/json","x-thirdweb-client-id":n.clientId,Authorization:`Bearer embedded-wallet-token:${t}`}});if(!s.ok)throw new Error(`Failed to generate wallet - ${s.status} ${s.statusText}`);const{wallet:o}=await s.json();return o}class Ae{constructor({baseUrl:e,querier:t,preLogin:a,postLogin:s,client:o,ecosystem:i}){Object.defineProperty(this,"LoginQuerier",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"preLogin",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"postLogin",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"baseUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ecosystem",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.baseUrl=e,this.LoginQuerier=t,this.preLogin=a,this.postLogin=s,this.client=o,this.ecosystem=i}async sendEmailLoginOtp({email:e}){return await this.LoginQuerier.call({procedureName:"sendThirdwebEmailLoginOtp",params:{email:e}})}async sendSmsLoginOtp({phoneNumber:e}){return await this.LoginQuerier.call({procedureName:"sendThirdwebSmsLoginOtp",params:{phoneNumber:e}})}}class Pe extends Ae{async authenticateWithModal(){return this.LoginQuerier.call({procedureName:"loginWithThirdwebModal",params:void 0,showIframe:!0})}async loginWithModal(){await this.preLogin();const e=await this.authenticateWithModal();return this.postLogin(e)}async authenticateWithIframe({email:e}){return this.LoginQuerier.call({procedureName:"loginWithThirdwebModal",params:{email:e},showIframe:!0})}async loginWithIframe({email:e}){await this.preLogin();const t=await this.authenticateWithIframe({email:e});return this.postLogin(t)}async authenticateWithCustomJwt({encryptionKey:e,jwt:t}){if(!e||e.length===0)throw new Error("Encryption key is required for custom jwt auth");return this.LoginQuerier.call({procedureName:"loginWithCustomJwt",params:{encryptionKey:e,jwt:t}})}async loginWithCustomJwt({encryptionKey:e,jwt:t}){if(!e||e.length===0)throw new Error("Encryption key is required for custom jwt auth");await this.preLogin();const a=await this.authenticateWithCustomJwt({encryptionKey:e,jwt:t});return this.postLogin(a)}async authenticateWithCustomAuthEndpoint({encryptionKey:e,payload:t}){return this.LoginQuerier.call({procedureName:"loginWithCustomAuthEndpoint",params:{encryptionKey:e,payload:t}})}async loginWithCustomAuthEndpoint({encryptionKey:e,payload:t}){if(!e||e.length===0)throw new Error("Encryption key is required for custom auth");await this.preLogin();const a=await this.authenticateWithCustomAuthEndpoint({encryptionKey:e,payload:t});return this.postLogin(a)}async authenticateWithEmailOtp({email:e,otp:t,recoveryCode:a}){return this.LoginQuerier.call({procedureName:"verifyThirdwebEmailLoginOtp",params:{email:e,otp:t,recoveryCode:a}})}async loginWithEmailOtp({email:e,otp:t,recoveryCode:a}){const s=await this.authenticateWithEmailOtp({email:e,otp:t,recoveryCode:a});return this.postLogin(s)}async authenticateWithSmsOtp({phoneNumber:e,otp:t,recoveryCode:a}){return this.LoginQuerier.call({procedureName:"verifyThirdwebSmsLoginOtp",params:{phoneNumber:e,otp:t,recoveryCode:a}})}async loginWithSmsOtp({phoneNumber:e,otp:t,recoveryCode:a}){const s=await this.authenticateWithSmsOtp({phoneNumber:e,otp:t,recoveryCode:a});return this.postLogin(s)}}class Le{constructor({client:e,querier:t,onAuthSuccess:a,ecosystem:s,baseUrl:o,localStorage:i}){Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ecosystem",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"AuthQuerier",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"localStorage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onAuthSuccess",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"BaseLogin",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.client=e,this.ecosystem=s,this.AuthQuerier=t,this.localStorage=i,this.onAuthSuccess=a,this.BaseLogin=new Pe({postLogin:async r=>this.postLogin(r),preLogin:async()=>{await this.preLogin()},ecosystem:s,querier:t,client:e,baseUrl:o})}async preLogin(){await this.logout()}async postLogin({storedToken:e,walletDetails:t}){return e.shouldStoreCookieString&&await this.localStorage.saveAuthCookie(e.cookieString),await this.onAuthSuccess({storedToken:e,walletDetails:t})}async loginWithAuthToken(e,t){var o;e.storedToken.authProvider!=="Backend"&&await this.preLogin();const a=await E({authToken:e.storedToken.cookieString,client:this.client,ecosystem:this.ecosystem});if(!a)throw new Error("Cannot login, no user found for auth token");if(a.wallets.length>0&&((o=a.wallets[0])==null?void 0:o.type)==="enclave")return this.postLogin({storedToken:e.storedToken,walletDetails:{walletAddress:a.wallets[0].address}});if(a.wallets.length===0){const i=await Te({authToken:e.storedToken.cookieString,client:this.client,ecosystem:this.ecosystem});return this.postLogin({storedToken:e.storedToken,walletDetails:{walletAddress:i.address}})}const s=await this.AuthQuerier.call({procedureName:"loginWithStoredTokenDetails",params:{storedToken:e.storedToken,recoveryCode:t}});return this.postLogin(s)}async loginWithModal(){return this.BaseLogin.loginWithModal()}async authenticateWithModal(){return this.BaseLogin.authenticateWithModal()}async loginWithIframe(e){return this.BaseLogin.loginWithIframe(e)}async authenticateWithIframe(e){return this.BaseLogin.authenticateWithIframe(e)}async loginWithCustomJwt(e){return this.BaseLogin.loginWithCustomJwt(e)}async authenticateWithCustomJwt(e){return this.BaseLogin.authenticateWithCustomJwt(e)}async loginWithCustomAuthEndpoint(e){return this.BaseLogin.loginWithCustomAuthEndpoint(e)}async authenticateWithCustomAuthEndpoint(e){return this.BaseLogin.authenticateWithCustomAuthEndpoint(e)}async sendEmailLoginOtp({email:e}){return this.BaseLogin.sendEmailLoginOtp({email:e})}async sendSmsLoginOtp({phoneNumber:e}){return this.BaseLogin.sendSmsLoginOtp({phoneNumber:e})}async loginWithEmailOtp(e){return await this.preLogin(),this.BaseLogin.loginWithEmailOtp(e)}async authenticateWithEmailOtp(e){return this.BaseLogin.authenticateWithEmailOtp(e)}async loginWithSmsOtp(e){return await this.preLogin(),this.BaseLogin.loginWithSmsOtp(e)}async authenticateWithSmsOtp(e){return this.BaseLogin.authenticateWithSmsOtp(e)}async logout(){const e=await this.localStorage.removeAuthCookie(),t=await this.localStorage.removeWalletUserId();return{success:e||t}}}const We=async n=>{const{client:e,ecosystem:t}=n,a=P({client:e,ecosystem:t,authOption:n.strategy}),s={"Content-Type":"application/json","x-client-id":e.clientId};t!=null&&t.id&&(s["x-ecosystem-id"]=t.id),t!=null&&t.partnerId&&(s["x-ecosystem-partner-id"]=t.partnerId);const o=(()=>{switch(n.strategy){case"email":return{email:n.email};case"phone":return{phone:n.phoneNumber}}})(),i=await fetch(a,{method:"POST",headers:s,body:h(o)});if(!i.ok)throw new Error("Failed to send verification code");return await i.json()},j=async n=>{const{client:e,ecosystem:t}=n,a=W({authOption:n.strategy,client:n.client,ecosystem:n.ecosystem}),s={"Content-Type":"application/json","x-client-id":e.clientId};t!=null&&t.id&&(s["x-ecosystem-id"]=t.id),t!=null&&t.partnerId&&(s["x-ecosystem-partner-id"]=t.partnerId);const o=(()=>{switch(n.strategy){case"email":return{email:n.email,code:n.verificationCode};case"phone":return{phone:n.phoneNumber,code:n.verificationCode}}})(),i=await fetch(a,{method:"POST",headers:s,body:h(o)});if(!i.ok)throw new Error("Failed to verify verification code");return await i.json()};class D{constructor({client:e,ecosystem:t,querier:a,localStorage:s}){Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ecosystem",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"walletManagerQuerier",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"localStorage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.client=e,this.ecosystem=t,this.walletManagerQuerier=a,this.localStorage=s}async postWalletSetUp(e){e.deviceShareStored&&await this.localStorage.saveDeviceShare(e.deviceShareStored,e.storedToken.authDetails.userWalletId)}async getUserWalletStatus(){const e=await this.walletManagerQuerier.call({procedureName:"getUserStatus",params:void 0});return e.status==="Logged In, Wallet Initialized"?{status:"Logged In, Wallet Initialized",...e.user,account:await this.getAccount()}:e.status==="Logged In, New Device"?{status:"Logged In, New Device",...e.user}:e.status==="Logged In, Wallet Uninitialized"?{status:"Logged In, Wallet Uninitialized",...e.user}:{status:e.status}}async getAccount(){var i;const e=this.walletManagerQuerier,t=this.client,a=(i=this.ecosystem)==null?void 0:i.partnerId,{address:s}=await e.call({procedureName:"getAddress",params:void 0}),o=async r=>{const l={to:r.to??void 0,data:r.data,value:r.value,gasLimit:r.gas,nonce:r.nonce,chainId:r.chainId};r.maxFeePerGas?(l.accessList=r.accessList,l.maxFeePerGas=r.maxFeePerGas,l.maxPriorityFeePerGas=r.maxPriorityFeePerGas,l.type=2):(l.gasPrice=r.gasPrice,l.type=0);const c=C().rpc,{signedTransaction:u}=await e.call({procedureName:"signTransaction",params:{transaction:l,chainId:r.chainId,partnerId:a,rpcEndpoint:`https://${r.chainId}.${c}`}});return u};return{address:f(s),async signTransaction(r){if(!r.chainId)throw new Error("chainId required in tx to sign");return o({...r,chainId:r.chainId})},async sendTransaction(r){const l=v({client:t,chain:T(r.chainId)}),c=await o(r),u=await U(l,c);return $({client:t,chainId:r.chainId,walletAddress:s,walletType:"inApp",transactionHash:u,contractAddress:r.to??void 0,gasPrice:r.gasPrice}),{transactionHash:u}},async signMessage({message:r}){const l=typeof r=="string"?r:r.raw instanceof Uint8Array?r.raw:H(r.raw),{signedMessage:c}=await e.call({procedureName:"signMessage",params:{message:l,partnerId:a,chainId:1}});return c},async signTypedData(r){var O;const l=_(r);(O=l.types)!=null&&O.EIP712Domain&&(l.types.EIP712Domain=void 0);const c=l.domain,u=c==null?void 0:c.chainId,g={...c!=null&&c.verifyingContract?{verifyingContract:c==null?void 0:c.verifyingContract}:{},name:c==null?void 0:c.name,version:c==null?void 0:c.version};u&&(g.chainId=u);const y=C().rpc,{signedTypedData:R}=await e.call({procedureName:"signTypedDataV4",params:{domain:g,types:l.types,message:l.message,chainId:Number.parseInt(BigInt(u||1).toString()),partnerId:a,rpcEndpoint:`https://${u}.${y}`}});return R}}}}class De{isClientIdLegacyPaper(e){return e.indexOf("-")>0&&e.length===36}constructor({client:e,onAuthSuccess:t,ecosystem:a,passkeyDomain:s,storage:o}){if(Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ecosystem",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"querier",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"storage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"wallet",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"auth",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"passkeyDomain",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.isClientIdLegacyPaper(e.clientId))throw new Error("You are using a legacy clientId. Please use the clientId found on the thirdweb dashboard settings page");const i=p("inAppWallet");this.client=e,this.ecosystem=a,this.passkeyDomain=s,this.storage=new L({storage:o??Ee(),clientId:e.clientId,ecosystem:a}),this.querier=new Ie({clientId:e.clientId,ecosystem:a,baseUrl:i}),this.auth=new Le({client:e,querier:this.querier,baseUrl:i,localStorage:this.storage,ecosystem:a,onAuthSuccess:async r=>{if(t==null||t(r),r.storedToken.authDetails.walletType==="sharded"&&(await this.querier.call({procedureName:"migrateFromShardToEnclave",params:{storedToken:r.storedToken}})||console.warn("Failed to migrate from sharded to enclave wallet, continuing with sharded wallet")),this.wallet=await this.initializeWallet(r.storedToken.cookieString),!this.wallet)throw new Error("Failed to initialize wallet");const l="deviceShareStored"in r.walletDetails?r.walletDetails.deviceShareStored:void 0;return await this.wallet.postWalletSetUp({storedToken:r.storedToken,deviceShareStored:l}),this.wallet instanceof D&&await this.querier.call({procedureName:"initIframe",params:{partnerId:a==null?void 0:a.partnerId,ecosystemId:a==null?void 0:a.id,clientId:this.client.clientId,deviceShareStored:"deviceShareStored"in r.walletDetails?r.walletDetails.deviceShareStored:null,walletUserId:r.storedToken.authDetails.userWalletId,authCookie:r.storedToken.cookieString}}),{user:{status:"Logged In, Wallet Initialized",authDetails:r.storedToken.authDetails,account:await this.wallet.getAccount(),walletAddress:r.walletDetails.walletAddress}}}})}async initializeWallet(e){var s;const t=await this.storage.getAuthCookie();if(!e&&t===null)throw new Error("No auth token provided and no stored auth token found to initialize the wallet");const a=await E({authToken:e||t,client:this.client,ecosystem:this.ecosystem});if(!a)throw new Error("Cannot initialize wallet, no user logged in");if(a.wallets.length===0)throw new Error("Cannot initialize wallet, this user does not have a wallet generated yet");return((s=a.wallets[0])==null?void 0:s.type)==="enclave"?new ye({client:this.client,ecosystem:this.ecosystem,address:a.wallets[0].address,storage:this.storage}):new D({client:this.client,ecosystem:this.ecosystem,querier:this.querier,localStorage:this.storage})}async getUser(){if(!this.wallet){const e=await this.storage.getAuthCookie();if(!e)return{status:"Logged Out"};this.wallet=await this.initializeWallet(e)}if(!this.wallet)throw new Error("Wallet not initialized");return await this.wallet.getUserWalletStatus()}getAccount(){if(!this.wallet)throw new Error("Wallet not initialized");return this.wallet.getAccount()}async preAuthenticate(e){return We({...e,client:this.client,ecosystem:this.ecosystem})}async authenticateWithRedirect(e,t,a){return V({authOption:e,client:this.client,ecosystem:this.ecosystem,redirectUrl:a,mode:t})}async loginWithAuthToken(e,t){return this.auth.loginWithAuthToken(e,t)}async authenticate(e){const t=e.strategy;switch(t){case"email":return j({...e,client:this.client,ecosystem:this.ecosystem});case"phone":return j({...e,client:this.client,ecosystem:this.ecosystem});case"auth_endpoint":return ae({payload:e.payload,client:this.client,ecosystem:this.ecosystem});case"jwt":return re({jwt:e.jwt,client:this.client,ecosystem:this.ecosystem});case"passkey":return this.passkeyAuth(e);case"iframe_email_verification":return this.auth.authenticateWithIframe({email:e.email});case"iframe":return this.auth.authenticateWithModal();case"apple":case"facebook":case"google":case"telegram":case"github":case"twitch":case"farcaster":case"line":case"x":case"steam":case"coinbase":case"discord":return J({authOption:t,client:this.client,ecosystem:this.ecosystem,closeOpenedWindow:e.closeOpenedWindow,openedWindow:e.openedWindow});case"guest":return ie({client:this.client,ecosystem:this.ecosystem,storage:S});case"backend":return ne({client:this.client,walletSecret:e.walletSecret,ecosystem:this.ecosystem});case"wallet":return he({ecosystem:this.ecosystem,client:this.client,wallet:e.wallet,chain:e.chain})}}async connect(e){const t=e.strategy;switch(t){case"auth_endpoint":case"jwt":{const a=await this.authenticate(e);return await this.loginWithAuthToken(a,e.encryptionKey)}case"iframe_email_verification":return this.auth.loginWithIframe({email:e.email});case"iframe":return this.auth.loginWithModal();case"passkey":{const a=await this.passkeyAuth(e);return this.loginWithAuthToken(a)}case"backend":case"phone":case"email":case"wallet":case"apple":case"facebook":case"google":case"farcaster":case"telegram":case"github":case"line":case"x":case"guest":case"coinbase":case"twitch":case"steam":case"discord":{const a=await this.authenticate(e);return await this.auth.loginWithAuthToken(a)}default:Se(t)}}async logout(){return await this.auth.logout()}async passkeyAuth(e){const{PasskeyWebClient:t}=await F(async()=>{const{PasskeyWebClient:r}=await import("./index-CLtqW0Fd.js").then(l=>l.eJ);return{PasskeyWebClient:r}},__vite__mapDeps([1,2])),{passkeyName:a,storeLastUsedPasskey:s=!0}=e,o=new t,i=this.storage;return e.type==="sign-up"?le({client:this.client,ecosystem:this.ecosystem,username:a,passkeyClient:o,storage:s?i:void 0,rp:{id:this.passkeyDomain??window.location.hostname,name:this.passkeyDomain??window.document.title}}):ue({client:this.client,ecosystem:this.ecosystem,passkeyClient:o,storage:s?i:void 0,rp:{id:this.passkeyDomain??window.location.hostname,name:this.passkeyDomain??window.document.title}})}async linkProfile(e){const{storedToken:t}=await this.authenticate(e);return await se({client:e.client,tokenToLink:t.cookieString,storage:this.storage,ecosystem:e.ecosystem||this.ecosystem})}async unlinkProfile(e){return await oe({client:this.client,storage:this.storage,ecosystem:this.ecosystem,profileToUnlink:e})}async getProfiles(){return ce({client:this.client,ecosystem:this.ecosystem,storage:this.storage})}}function Se(n,e){throw new Error(`Invalid param: ${n}`)}function Ee(){return typeof window<"u"&&window.localStorage?S:X}export{De as InAppWebConnector};
