import{N as f,dQ as r,G as m,dR as p,a1 as l,r as c}from"./index-pmk3GmSA.js";async function d(e,s){return await e({method:"eth_getStorageAt",params:[f(s.address),s.position,s.blockTag??"latest"]})}function u(e){if(e.startsWith("0x")||(e=`0x${e}`),e.startsWith("0x363d3d373d3d3d363d73"))return`0x${e.slice(22,62)}`;if(e.startsWith("0x36603057343d5230"))return`0x${e.slice(122,162)}`;if(e.startsWith("0x3d3d3d3d363d3d37363d73"))return`0x${e.slice(24,64)}`;if(e.startsWith("0x366000600037611000600036600073"))return`0x${e.slice(32,72)}`;if(e.startsWith("0x36600080376020600036600073"))return`0x${e.slice(28,68)}`;if(e.startsWith("0x365f5f375f5f365f73"))return`0x${e.slice(20,60)}`;if(e.length===48&&e.startsWith("0xef0100"))return`0x${e.slice(8,48)}`}const o="******************************************",x="******************************************000000000000000000000000";async function y(e){const[s,a]=await Promise.all([r(e),A(e)]),i=u(s);if(i)return{address:i,bytecode:await r(m({...e,address:i}))};let t;if(a&&a!==o?(e=m({...e,address:a}),t=await S(e)):t=await g(e),t&&p(t)&&t!==o){const n=await r({...e,address:t});return n==="0x"?{address:e.address,bytecode:s}:{address:t,bytecode:n}}return{address:e.address,bytecode:s}}async function A(e){const s=l({client:e.client,chain:e.chain});try{return`0x${(await d(s,{address:e.address,position:"0xa3f0ad74e5423aebfd80d3ef4346578335a9a72aeaee59ff6cb3582b35133d50"})).slice(-40)}`}catch{return}}async function g(e){const s=l({client:e.client,chain:e.chain});try{const a=[d(s,{address:e.address,position:"0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc"}),d(s,{address:e.address,position:"0xbaab7dbf64751104133af04abc7d9979f0fda3b059a322a8333f533d3f32bf7f"}),d(s,{address:e.address,position:"0x7050c9e0f4ca769c69bd3a8ef740bc37934f8e2c036e5a723fd8ee048ed3f8c3"})],t=(await Promise.all(a)).find(n=>n!==x);return t?`0x${t.slice(-40)}`:o}catch{return}}const h={type:"function",name:"implementation",inputs:[],outputs:[{type:"address",name:"",internalType:"address"}],stateMutability:"view"};async function S(e){try{return await c({contract:e,method:h})}catch{return}}export{y as resolveImplementation};
