import{j as p,c as y,G as c,r as w}from"./index-pmk3GmSA.js";async function u({account:e,serializableTransaction:a,transaction:n,gasless:s}){const t=c({address:s.relayer<PERSON>orwarderAddress,chain:n.chain,client:n.client}),r=await w({contract:t,method:"function getNonce(address) view returns (uint256)",params:[e.address]}),[o,m]=await(async()=>{if(!a.to)throw new Error("OpenZeppelin transactions must have a 'to' address");if(!a.gas)throw new Error("OpenZeppelin transactions must have a 'gas' value");if(!a.data)throw new Error("OpenZeppelin transactions must have a 'data' value");if(s.experimentalChainlessSupport){const i={from:e.address,to:a.to,value:0n,gas:a.gas,nonce:r,data:a.data,chainid:BigInt(n.chain.id)};return[await e.signTypedData({domain:{name:"GSNv2 Forwarder",version:"0.0.1",verifyingContract:t.address},message:i,primaryType:"ForwardRequest",types:{ForwardRequest:h}}),i]}const d={from:e.address,to:a.to,value:0n,gas:a.gas,nonce:r,data:a.data};return[await e.signTypedData({domain:{name:s.domainName??"GSNv2 Forwarder",version:s.domainVersion??"0.0.1",chainId:n.chain.id,verifyingContract:t.address},message:d,primaryType:"ForwardRequest",types:{ForwardRequest:g}}),d]})();return{message:m,signature:o,messageType:"forward"}}const g=[{name:"from",type:"address"},{name:"to",type:"address"},{name:"value",type:"uint256"},{name:"gas",type:"uint256"},{name:"nonce",type:"uint256"},{name:"data",type:"bytes"}],h=[{name:"from",type:"address"},{name:"to",type:"address"},{name:"value",type:"uint256"},{name:"gas",type:"uint256"},{name:"nonce",type:"uint256"},{name:"data",type:"bytes"},{name:"chainid",type:"uint256"}];async function l(e){const{message:a,messageType:n,signature:s}=await u(e),t=await fetch(e.gasless.relayerUrl,{method:"POST",body:p({request:a,type:n,signature:s,forwarderAddress:e.gasless.relayerForwarderAddress})});if(!t.ok)throw new Error(`Failed to send transaction: ${await t.text()}`);const r=await t.json();if(!r.result)throw new Error(`Relay transaction failed: ${r.message}`);const o=JSON.parse(r.result).txHash;if(y(o))return{transactionHash:o,chain:e.transaction.chain,client:e.transaction.client};throw new Error(`Failed to send transaction: ${p(r)}`)}export{u as prepareOpenZeppelinTransaction,l as relayOpenZeppelinTransaction};
