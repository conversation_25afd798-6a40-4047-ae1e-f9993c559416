const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ownerOf-DLj4JqoD.js","assets/index-pmk3GmSA.js","assets/index-BUaFfTib.css","assets/detectExtension-BgBpH7dZ.js"])))=>i.map(i=>d[i]);
import{r as d,dS as u,_ as o,dT as a}from"./index-pmk3GmSA.js";import{f as s}from"./fetchTokenMetadata-B9e5moy8.js";const I="0xc87b56dd",k=[{type:"uint256",name:"_tokenId"}],l=[{type:"string"}];async function y(t){return d({contract:t.contract,method:[I,k,l],params:[t.tokenId]})}const f="0x4f6ccce7",h=[{type:"uint256",name:"_index"}],T=[{type:"uint256"}];async function _(t){return d({contract:t.contract,method:[f,h,T],params:[t.index]})}async function w(t){const{useIndexer:c=!0}=t;if(c)try{return await m(t)}catch{return await e(t)}return await e(t)}async function m(t){const c=await u({client:t.contract.client,chain:t.contract.chain,contractAddress:t.contract.address,tokenId:t.tokenId,includeOwners:t.includeOwner});return c||e(t)}async function e(t){let c=t.tokenId;if(t.tokenByIndex)try{c=await _({contract:t.contract,index:t.tokenId})}catch{}const[n,r]=await Promise.all([y({contract:t.contract,tokenId:c}).catch(()=>null),t.includeOwner?o(()=>import("./ownerOf-DLj4JqoD.js"),__vite__mapDeps([0,1,2,3])).then(i=>i.ownerOf({contract:t.contract,tokenId:c})).catch(()=>null):null]);return n!=null&&n.trim()?a(await s({client:t.contract.client,tokenId:c,tokenUri:n}).catch(()=>({id:c,type:"ERC721",uri:n})),{tokenId:c,tokenUri:n,type:"ERC721",owner:r,tokenAddress:t.contract.address,chainId:t.contract.chain.id}):a({id:c,type:"ERC721",uri:""},{tokenId:c,tokenUri:"",type:"ERC721",owner:r,tokenAddress:t.contract.address,chainId:t.contract.chain.id})}export{w as getNFT};
