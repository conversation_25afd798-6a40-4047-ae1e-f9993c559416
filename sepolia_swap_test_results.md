# Sepolia Swap Test Results

## Test Objective
Test swapping 1 USDC with USDT on Sepolia testnet and check what chain the response returns.

## Curl Requests Tested

### 1. Sepolia USDC to USDT (Failed)
```bash
curl -X POST http://localhost:5000/api/bridge/quote \
  -H "Content-Type: application/json" \
  -d '{
    "originChainId": 11155111,
    "originTokenAddress": "******************************************",
    "destinationChainId": 11155111,
    "destinationTokenAddress": "******************************************",
    "amount": "1000000"
  }'
```

**Response:**
```json
{
  "error": "Failed to get bridge quote",
  "details": "ROUTE_NOT_FOUND | No route is available for the provided intent. Use the `/routes` endpoint to get available routes."
}
```

### 2. Sepolia USDC to ETH (Failed)
```bash
curl -X POST http://localhost:5000/api/bridge/quote \
  -H "Content-Type: application/json" \
  -d '{
    "originChainId": 11155111,
    "originTokenAddress": "******************************************",
    "destinationChainId": 11155111,
    "destinationTokenAddress": "******************************************",
    "amount": "1000000"
  }'
```

**Response:**
```json
{
  "error": "Failed to get bridge quote",
  "details": "ROUTE_NOT_FOUND | No route is available for the provided intent."
}
```

### 3. Ethereum Mainnet USDC to USDT (Success)
```bash
curl -X POST http://localhost:5000/api/bridge/quote \
  -H "Content-Type: application/json" \
  -d '{
    "originChainId": 1,
    "originTokenAddress": "******************************************",
    "destinationChainId": 1,
    "destinationTokenAddress": "******************************************",
    "amount": "1000000"
  }'
```

**Response:**
```json
{
  "success": true,
  "quote": {
    "originAmount": "1000000",
    "destinationAmount": "996174",
    "blockNumber": "22628889",
    "timestamp": 1749011228295,
    "estimatedExecutionTimeMs": 12000,
    "steps": [
      {
        "originToken": {
          "chainId": 1,
          "address": "******************************************",
          "symbol": "USDC",
          "name": "USD Coin",
          "decimals": 6,
          "priceUsd": 0.99975,
          "iconUri": "https://coin-images.coingecko.com/coins/images/6319/large/usdc.png?1696506694"
        },
        "destinationToken": {
          "chainId": 1,
          "address": "******************************************",
          "symbol": "USDT",
          "name": "Tether USD",
          "decimals": 6,
          "priceUsd": 1.000643,
          "iconUri": "https://coin-images.coingecko.com/coins/images/39963/large/usdt.png?1724952731"
        },
        "originAmount": "1000000",
        "destinationAmount": "996174",
        "estimatedExecutionTimeMs": 12000
      }
    ],
    "intent": {
      "originChainId": 1,
      "originTokenAddress": "******************************************",
      "destinationChainId": 1,
      "destinationTokenAddress": "******************************************",
      "amount": "1000000"
    }
  }
}
```

## Key Findings

### Chain Information Returned
- **originToken.chainId**: `1` (Ethereum mainnet)
- **destinationToken.chainId**: `1` (Ethereum mainnet)
- **intent.originChainId**: `1` (Ethereum mainnet)
- **intent.destinationChainId**: `1` (Ethereum mainnet)

### Sepolia Testnet Support
- ❌ Sepolia testnet (chain ID 11155111) is **NOT supported** by thirdweb's bridge API
- All Sepolia swap attempts returned "ROUTE_NOT_FOUND" errors
- This includes USDC→USDT, USDC→ETH, and cross-chain swaps from Sepolia

### Token Addresses Used
- **Sepolia USDC**: `******************************************`
- **Sepolia USDT**: `******************************************`
- **Ethereum USDC**: `******************************************`
- **Ethereum USDT**: `******************************************`

## Conclusion
The thirdweb bridge API correctly returns chain information in successful responses, but does not support swaps on Sepolia testnet. For testing purposes, use Ethereum mainnet or other supported networks.
