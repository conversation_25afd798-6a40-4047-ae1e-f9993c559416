import { createThirdwebClient } from "thirdweb";
import { createWallet, inAppWallet } from "thirdweb/wallets";
import {
  ethereum,
  polygon,
  sepolia,
  polygonAmoy,
  arbitrum,
  optimism,
  base,
  bsc,
  avalanche,
  fantom,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  bscTestnet,
} from "thirdweb/chains";

// Get the client ID from environment variables
const clientId = import.meta.env.VITE_THIRDWEB_CLIENT_ID;

if (!clientId) {
  throw new Error(
    "VITE_THIRDWEB_CLIENT_ID is not set in environment variables"
  );
}

// Create the shared Thirdweb client
export const client = createThirdwebClient({
  clientId,
});

// Helper function to determine if a chain is a testnet
const isTestnet = (chainId: number): boolean => {
  const testnetChainIds = [
    ********, // Sepolia
    421614, // Arbitrum Sepolia
    ********, // Optimism Sepolia
    84532, // Base Sepolia
    80002, // Polygon Amoy
    97, // BSC Testnet
  ];
  return testnetChainIds.includes(chainId);
};

// Helper function to create smart account config with conditional gas sponsoring
export const createSmartAccountConfig = (chain: any) => ({
  chain,
  sponsorGas: isTestnet(chain.id), // Only sponsor gas on testnets (free)
});

// Helper function to check if gas sponsoring is available for a chain
export const isGasSponsoringAvailable = (chainId: number): boolean => {
  return isTestnet(chainId);
};

// Helper function to create dynamic smart account config based on current chain
export const createDynamicSmartAccountConfig = () => {
  // Default to sepolia for smart accounts (testnet with gas sponsoring)
  return createSmartAccountConfig(sepolia);
};

// Define supported wallets with error handling
export const wallets = [
  // In-app wallet with social login options + Smart Account (EIP-7702)
  inAppWallet({
    auth: {
      options: [
        "google",
        "apple",
        "facebook",
        "github",
        "email",
        "phone",
        "passkey",
      ],
      mode: "popup",
    },
    smartAccount: createDynamicSmartAccountConfig(), // Dynamic gas sponsoring based on chain
  }),
  // Traditional wallet connectors
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  // createWallet("io.rabby"),
  // createWallet("me.rainbow"),
  // createWallet("io.zerion.wallet"),
  createWallet("walletConnect"),
];

// Recommended wallets for better ordering in the modal
export const recommendedWallets = [
  inAppWallet({
    auth: {
      options: [
        "google",
        "apple",
        "facebook",
        "github",
        "email",
        "phone",
        "passkey",
      ],
      mode: "popup",
    },
    smartAccount: createDynamicSmartAccountConfig(), // Dynamic gas sponsoring based on chain
  }),
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("io.rabby"),
  createWallet("me.rainbow"),
];

// Add wallet connection error handler
if (typeof window !== "undefined") {
  // Override console.error temporarily to catch and handle wallet extension errors
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args.join(" ");
    if (
      message.includes(
        "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"
      )
    ) {
      // Log as info instead of error for this specific case since it's harmless
      console.info(
        "🔧 Browser extension message channel (suppressed):",
        ...args
      );
      return;
    }
    // Call original console.error for other errors
    originalConsoleError.apply(console, args);
  };

  console.info("🔧 ThirdWeb wallet error handler initialized");
}

// Define supported chains (including testnet chains as per user preference)
export const chains = [
  // Ethereum and Layer 2s
  ethereum,
  arbitrum,
  optimism,
  base,

  // Alternative Layer 1s
  polygon,
  bsc,
  avalanche,
  fantom,

  // Testnets
  sepolia,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  polygonAmoy,
  bscTestnet,
];

// Export individual chains for easy access
export {
  ethereum,
  polygon,
  sepolia,
  polygonAmoy,
  arbitrum,
  optimism,
  base,
  bsc,
  avalanche,
  fantom,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  bscTestnet,
};
