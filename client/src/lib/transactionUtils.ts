import type { TransactionData } from "@/hooks/useTransactionHistory";

export interface ActivityItem {
  id: string;
  type: "chat" | "transaction" | "contract_interaction";
  title: string;
  description: string;
  timestamp: Date;
  status?: "pending" | "success" | "failed";
  chainId?: string;
  txHash?: string;
  chatId?: number;
  value?: string;
  from?: string;
  to?: string;
  gasUsed?: string;
  tokenTransfers?: Array<{
    from: string;
    to: string;
    value: string;
    tokenAddress: string;
    tokenSymbol?: string;
    tokenName?: string;
  }>;
}

/**
 * Transform transaction data into activity items
 */
export function transformTransactionToActivity(
  tx: TransactionData,
  userAddress: string
): ActivityItem {
  const isOutgoing = tx.from.toLowerCase() === userAddress.toLowerCase();
  const hasTokenTransfers = tx.tokenTransfers && tx.tokenTransfers.length > 0;
  
  // Determine transaction type and title
  let type: ActivityItem["type"] = "transaction";
  let title = "Transaction";
  let description = "";

  if (hasTokenTransfers) {
    type = "contract_interaction";
    const transfer = tx.tokenTransfers![0];
    
    if (tx.functionName) {
      switch (tx.functionName) {
        case "transfer":
        case "transferFrom":
          title = `${transfer.tokenSymbol || "Token"} Transfer`;
          description = isOutgoing 
            ? `Sent ${formatTokenAmount(transfer.value)} ${transfer.tokenSymbol || "tokens"} to ${formatAddress(transfer.to)}`
            : `Received ${formatTokenAmount(transfer.value)} ${transfer.tokenSymbol || "tokens"} from ${formatAddress(transfer.from)}`;
          break;
        case "approve":
          title = `${transfer.tokenSymbol || "Token"} Approval`;
          description = `Approved ${formatTokenAmount(transfer.value)} ${transfer.tokenSymbol || "tokens"} for ${formatAddress(tx.to)}`;
          break;
        case "swapExactETHForTokens":
        case "swapExactTokensForTokens":
        case "swapTokensForExactTokens":
          title = "Token Swap";
          description = `Swapped tokens on ${getProtocolName(tx.to)}`;
          break;
        case "addLiquidity":
        case "addLiquidityETH":
          title = "Add Liquidity";
          description = `Added liquidity to ${getProtocolName(tx.to)}`;
          break;
        case "removeLiquidity":
          title = "Remove Liquidity";
          description = `Removed liquidity from ${getProtocolName(tx.to)}`;
          break;
        case "mint":
          title = "Token Mint";
          description = `Minted ${formatTokenAmount(transfer.value)} ${transfer.tokenSymbol || "tokens"}`;
          break;
        default:
          title = `${transfer.tokenSymbol || "Token"} Interaction`;
          description = `Interacted with ${transfer.tokenSymbol || "token"} contract`;
      }
    } else {
      title = `${transfer.tokenSymbol || "Token"} Transfer`;
      description = isOutgoing 
        ? `Sent ${formatTokenAmount(transfer.value)} ${transfer.tokenSymbol || "tokens"}`
        : `Received ${formatTokenAmount(transfer.value)} ${transfer.tokenSymbol || "tokens"}`;
    }
  } else {
    // Native token transfer
    const ethValue = formatEthValue(tx.value);
    if (parseFloat(ethValue) > 0) {
      title = isOutgoing ? "ETH Sent" : "ETH Received";
      description = isOutgoing 
        ? `Sent ${ethValue} ETH to ${formatAddress(tx.to)}`
        : `Received ${ethValue} ETH from ${formatAddress(tx.from)}`;
    } else {
      title = "Contract Interaction";
      description = `Interacted with ${formatAddress(tx.to)}`;
      type = "contract_interaction";
    }
  }

  return {
    id: `tx-${tx.hash}`,
    type,
    title,
    description,
    timestamp: tx.timestamp,
    status: tx.status,
    chainId: tx.chainId.toString(),
    txHash: tx.hash,
    value: tx.value,
    from: tx.from,
    to: tx.to,
    gasUsed: tx.gasUsed,
    tokenTransfers: tx.tokenTransfers,
  };
}

/**
 * Format Ethereum address for display
 */
export function formatAddress(address: string): string {
  if (!address) return "";
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

/**
 * Format ETH value from wei to readable format
 */
export function formatEthValue(weiValue: string): string {
  try {
    const wei = BigInt(weiValue || "0");
    const eth = Number(wei) / 1e18;
    
    if (eth === 0) return "0";
    if (eth < 0.0001) return "< 0.0001";
    if (eth < 1) return eth.toFixed(4);
    if (eth < 1000) return eth.toFixed(3);
    
    return eth.toLocaleString(undefined, { maximumFractionDigits: 2 });
  } catch {
    return "0";
  }
}

/**
 * Format token amount for display
 */
export function formatTokenAmount(value: string, decimals = 18): string {
  try {
    const amount = BigInt(value || "0");
    const divisor = BigInt(10 ** decimals);
    const tokenAmount = Number(amount) / Number(divisor);
    
    if (tokenAmount === 0) return "0";
    if (tokenAmount < 0.0001) return "< 0.0001";
    if (tokenAmount < 1) return tokenAmount.toFixed(4);
    if (tokenAmount < 1000) return tokenAmount.toFixed(2);
    
    return tokenAmount.toLocaleString(undefined, { maximumFractionDigits: 2 });
  } catch {
    return "0";
  }
}

/**
 * Get protocol name from contract address
 */
export function getProtocolName(contractAddress: string): string {
  const knownProtocols: Record<string, string> = {
    // Uniswap V2
    "******************************************": "Uniswap V2",
    // Uniswap V3
    "0xe592427a0aece92de3edee1f18e0157c05861564": "Uniswap V3",
    // SushiSwap
    "0xd9e1ce17f2641f24ae83637ab66a2cca9c378b9f": "SushiSwap",
    // 1inch
    "0x1111111254fb6c44bac0bed2854e76f90643097d": "1inch",
    // Curve
    "0x99a58482bd75cbab83b27ec03ca68ff489b5788f": "Curve",
    // Balancer
    "******************************************": "Balancer",
    // Compound
    "******************************************": "Compound",
    // Aave
    "******************************************": "Aave",
  };

  return knownProtocols[contractAddress.toLowerCase()] || "DeFi Protocol";
}

/**
 * Get chain name from chain ID
 */
export function getChainName(chainId: number): string {
  const chainNames: Record<number, string> = {
    1: "Ethereum",
    137: "Polygon",
    56: "BSC",
    43114: "Avalanche",
    250: "Fantom",
    42161: "Arbitrum",
    10: "Optimism",
    8453: "Base",
    11155111: "Sepolia",
    80002: "Polygon Amoy",
    97: "BSC Testnet",
    421614: "Arbitrum Sepolia",
    11155420: "Optimism Sepolia",
    84532: "Base Sepolia",
  };

  return chainNames[chainId] || `Chain ${chainId}`;
}

/**
 * Format time ago string
 */
export function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks}w ago`;
  }

  return date.toLocaleDateString();
}

/**
 * Sort activities by timestamp (most recent first)
 */
export function sortActivitiesByTime(activities: ActivityItem[]): ActivityItem[] {
  return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
}
