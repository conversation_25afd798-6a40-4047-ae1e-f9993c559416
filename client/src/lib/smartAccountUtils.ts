import type { Wallet } from "thirdweb/wallets";

/**
 * Utility functions for handling smart account operations and error recovery
 */

export interface SmartAccountError extends Error {
  code?: number;
  reason?: string;
}

/**
 * Check if a wallet is a smart account and properly configured
 */
export async function checkSmartAccountStatus(wallet: Wallet): Promise<{
  isSmartAccount: boolean;
  isAuthorized: boolean;
  needsSetup: boolean;
}> {
  try {
    // In thirdweb v5, we check if it's a smart account by looking at the wallet ID
    // and checking if it has smart account features
    const isSmartAccount = wallet.id === "inApp" || wallet.id.includes("smart");
    const account = wallet.getAccount();

    return {
      isSmartAccount,
      isAuthorized: !!account,
      needsSetup: isSmartAccount && !account,
    };
  } catch (error) {
    console.error("Error checking smart account status:", error);
    return {
      isSmartAccount: false,
      isAuthorized: false,
      needsSetup: false,
    };
  }
}

/**
 * Handle smart account authorization errors and attempt recovery
 */
export async function handleSmartAccountError(
  error: SmartAccountError,
  wallet: Wallet
): Promise<{ shouldRetry: boolean; errorMessage: string }> {
  console.error("Smart account error:", error);

  // Handle 4100 error (unauthorized)
  if (error.code === 4100) {
    try {
      console.log("Attempting to recover from 4100 error...");

      // Try to reconnect the wallet
      await wallet.disconnect();
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait 1 second

      // For thirdweb v5, wallet.connect() might not be available on all wallet types
      // We'll try to trigger reconnection through the wallet's internal methods
      try {
        if ("connect" in wallet && typeof wallet.connect === "function") {
          await (wallet as any).connect();
        }
      } catch (connectError) {
        console.log(
          "Direct connect failed, wallet may auto-reconnect:",
          connectError
        );
      }

      // Check if we now have a valid account
      const newAccount = wallet.getAccount();
      if (newAccount) {
        console.log("Smart account recovery successful");
        return {
          shouldRetry: true,
          errorMessage: "",
        };
      } else {
        return {
          shouldRetry: false,
          errorMessage:
            "Smart account authorization failed. Please disconnect and reconnect your wallet to enable smart account features.",
        };
      }
    } catch (recoveryError) {
      console.error("Smart account recovery failed:", recoveryError);
      return {
        shouldRetry: false,
        errorMessage:
          "Failed to recover smart account. Please disconnect and reconnect your wallet.",
      };
    }
  }

  // Handle other smart account errors
  if (error.message?.includes("User rejected")) {
    return {
      shouldRetry: false,
      errorMessage: "Transaction was cancelled by user.",
    };
  }

  if (error.message?.includes("insufficient funds")) {
    return {
      shouldRetry: false,
      errorMessage:
        "Insufficient funds for transaction. Note: Smart accounts should have sponsored gas.",
    };
  }

  // Generic error handling
  return {
    shouldRetry: false,
    errorMessage:
      error.message || "Smart account transaction failed. Please try again.",
  };
}

/**
 * Prepare smart account for transactions
 */
export async function prepareSmartAccount(wallet: Wallet): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const status = await checkSmartAccountStatus(wallet);

    if (!status.isSmartAccount) {
      // Not a smart account, no preparation needed
      return { success: true };
    }

    if (status.needsSetup) {
      console.log("Smart account needs setup, attempting to connect...");

      // Try to connect the wallet
      try {
        if ("connect" in wallet && typeof wallet.connect === "function") {
          await (wallet as any).connect();
        }
      } catch (connectError) {
        console.log("Wallet connect failed:", connectError);
      }

      // Verify connection
      const account = wallet.getAccount();
      if (!account) {
        return {
          success: false,
          error:
            "Failed to set up smart account. Please try reconnecting your wallet.",
        };
      }
    }

    return { success: true };
  } catch (error: any) {
    console.error("Failed to prepare smart account:", error);
    return {
      success: false,
      error: error.message || "Failed to prepare smart account",
    };
  }
}

/**
 * Get user-friendly error message for smart account issues
 */
export function getSmartAccountErrorMessage(error: SmartAccountError): string {
  if (error.code === 4100) {
    return "Smart account authorization required. Please disconnect and reconnect your wallet to enable gasless transactions.";
  }

  if (error.message?.includes("User rejected")) {
    return "Transaction was cancelled by user.";
  }

  if (error.message?.includes("insufficient funds")) {
    return "Insufficient funds for transaction. Smart accounts should have sponsored gas - please check your configuration.";
  }

  if (error.message?.includes("network")) {
    return "Network error. Please check your connection and try again.";
  }

  return error.message || "Smart account transaction failed. Please try again.";
}
