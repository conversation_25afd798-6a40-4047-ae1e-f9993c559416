import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import TransactionBox from "@/components/TransactionBox";
import ThirdwebTransactionBox from "@/components/ThirdwebTransactionBox";
import ThirdwebSwapBox from "@/components/ThirdwebSwapBox";
import ThirdwebSwapDetailsTable from "@/components/ThirdwebSwapDetailsTable";
import ThirdwebApprovalBox from "@/components/ThirdwebApprovalBox";
import ThirdwebSwapFlow from "@/components/ThirdwebSwapFlow";
import TransactionTest from "@/components/TransactionTest";
import { useActiveAccount } from "thirdweb/react";

const ThirdwebComponentsDemo = () => {
  const activeAccount = useActiveAccount();
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  // Sample transaction data
  const sampleTransactionData = {
    from:
      activeAccount?.address || "******************************************",
    to: "******************************************",
    value: "1000000000000000000", // 1 ETH in wei
    chainId: 1,
    data: "0xa9059cbb0000000000000000000000002554a8d70e5e8b7c8c8c8c8c8c8c8c8c8c8c07a10000000000000000000000000000000000000000000000000de0b6b3a7640000",
    description: "Transfer 1 ETH to recipient address",
    action: "transfer",
  };

  // Sample swap data
  const sampleSwapData = {
    fromToken: {
      symbol: "USDC",
      address: "******************************************",
      amount: "1000",
      decimals: 6,
    },
    toToken: {
      symbol: "USDT",
      address: "******************************************",
      amount: "999.32",
      decimals: 6,
    },
    chainId: 1,
    estimatedGas: "150000",
    slippage: "0.5",
  };

  // Sample swap details data
  const sampleSwapDetailsData = {
    fromToken: {
      chainId: 1,
      symbol: "USDC",
      address: "******************************************",
      amount: "1000.000000",
    },
    toToken: {
      chainId: 1,
      symbol: "USDT",
      address: "******************************************",
      amount: "999.320000",
    },
    fromAddress:
      activeAccount?.address || "******************************************",
    toAddress:
      activeAccount?.address || "******************************************",
    sourceTxHash:
      "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    status: "completed" as const,
  };

  // Sample swap flow data (for approval + swap)
  const sampleSwapFlowData = {
    fromToken: {
      symbol: "USDC",
      address: "******************************************",
      amount: "1",
      decimals: 6,
    },
    toToken: {
      symbol: "USDT",
      address: "******************************************",
      amount: "0.995408",
      decimals: 6,
    },
    chainId: 1,
    routerAddress: "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D", // Uniswap V2 Router
    needsApproval: true,
  };

  const handleExecute = async (txData: any) => {
    console.log("Executing transaction:", txData);
    // Simulate transaction execution
    await new Promise((resolve) => setTimeout(resolve, 2000));
    const mockTxHash = "0x" + Math.random().toString(16).substr(2, 64);
    setTransactionHash(mockTxHash);
    return mockTxHash;
  };

  const resetDemo = () => {
    setTransactionHash(null);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Thirdweb Components Demo</h1>
        <p className="text-muted-foreground">
          Compare the old custom components with the new thirdweb-powered
          components
        </p>
        {activeAccount ? (
          <Badge variant="outline" className="bg-green-500/10 text-green-500">
            Wallet Connected: {activeAccount.address.slice(0, 6)}...
            {activeAccount.address.slice(-4)}
          </Badge>
        ) : (
          <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500">
            No Wallet Connected
          </Badge>
        )}
      </div>

      <Tabs defaultValue="test" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="test">Transaction Test</TabsTrigger>
          <TabsTrigger value="nebula-flow">Nebula Flow</TabsTrigger>
          <TabsTrigger value="transaction">Transaction Boxes</TabsTrigger>
          <TabsTrigger value="swap">Swap Boxes</TabsTrigger>
          <TabsTrigger value="details">Swap Details</TabsTrigger>
        </TabsList>

        <TabsContent value="test" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🧪 Smart Account Transaction Test
                <Badge variant="default">Live Test</Badge>
              </CardTitle>
              <CardDescription>
                Test the actual transaction from the curl request: "send 1 amoy
                to ******************************************"
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <TransactionTest />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nebula-flow" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Nebula-Style Swap Flow
                <Badge variant="default">Thirdweb</Badge>
              </CardTitle>
              <CardDescription>
                Two-step flow exactly like Nebula: Approval box + Swap box
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ThirdwebSwapFlow
                swapData={sampleSwapFlowData}
                onSwapComplete={(txHash) => {
                  console.log("Swap completed:", txHash);
                  setTransactionHash(txHash);
                }}
              />
            </CardContent>
          </Card>

          <div className="flex justify-center">
            <Button onClick={resetDemo} variant="outline">
              Reset Demo
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="transaction" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  Old TransactionBox
                  <Badge variant="secondary">Custom</Badge>
                </CardTitle>
                <CardDescription>
                  Original custom transaction component
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TransactionBox
                  title="ETH Transfer"
                  transactionData={sampleTransactionData}
                  onExecute={handleExecute}
                  transactionHash={transactionHash}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  New ThirdwebTransactionBox
                  <Badge variant="default">Thirdweb</Badge>
                </CardTitle>
                <CardDescription>
                  Enhanced with thirdweb's TransactionButton and real-time
                  status tracking
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ThirdwebTransactionBox
                  title="ETH Transfer"
                  transactionData={sampleTransactionData}
                  onExecute={handleExecute}
                  transactionHash={transactionHash}
                />
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-center">
            <Button onClick={resetDemo} variant="outline">
              Reset Demo
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="swap" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  ThirdwebSwapBox (Nebula Style)
                  <Badge variant="default">Thirdweb</Badge>
                </CardTitle>
                <CardDescription>
                  Matches Nebula's professional swap interface design
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ThirdwebSwapBox
                  title="Swap"
                  swapData={sampleSwapData}
                  onExecute={handleExecute}
                  transactionHash={transactionHash}
                  usePayEmbed={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  ThirdwebSwapBox (PayEmbed Mode)
                  <Badge variant="default">Thirdweb</Badge>
                </CardTitle>
                <CardDescription>
                  Using thirdweb's PayEmbed component for complete payment flow
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ThirdwebSwapBox
                  title="Swap"
                  swapData={sampleSwapData}
                  onExecute={handleExecute}
                  transactionHash={transactionHash}
                  usePayEmbed={true}
                />
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-center">
            <Button onClick={resetDemo} variant="outline">
              Reset Demo
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                ThirdwebSwapDetailsTable
                <Badge variant="default">Thirdweb</Badge>
              </CardTitle>
              <CardDescription>
                Detailed transaction information similar to thirdweb's
                SwapTxDetailsTable
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ThirdwebSwapDetailsTable
                swapData={sampleSwapDetailsData}
                hideStatusRow={false}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="bg-muted/30">
        <CardHeader>
          <CardTitle>Key Improvements</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm">
            <li>
              ✅ <strong>Real-time transaction status:</strong> Automatic
              receipt tracking and status updates
            </li>
            <li>
              ✅ <strong>Enhanced error handling:</strong> Better error messages
              and retry mechanisms
            </li>
            <li>
              ✅ <strong>Professional UI:</strong> Matches thirdweb's design
              patterns
            </li>
            <li>
              ✅ <strong>Built-in wallet integration:</strong> Seamless
              connection with thirdweb wallets
            </li>
            <li>
              ✅ <strong>Gas optimization:</strong> Automatic gas estimation and
              optimization
            </li>
            <li>
              ✅ <strong>Multi-chain support:</strong> Works across all
              supported networks
            </li>
            <li>
              ✅ <strong>PayEmbed integration:</strong> Optional use of
              thirdweb's complete payment flow
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThirdwebComponentsDemo;
