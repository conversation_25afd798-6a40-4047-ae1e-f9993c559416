import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  useActiveAccount,
  useActiveWallet,
  useSendTransaction,
} from "thirdweb/react";
import { sendTransaction, waitForReceipt } from "thirdweb";
import type { PreparedTransaction } from "thirdweb";
import {
  handleSmartAccountError,
  prepareSmartAccount,
  getSmartAccountErrorMessage,
  checkSmartAccountStatus,
  type SmartAccountError,
} from "@/lib/smartAccountUtils";
import { isGasSponsoringAvailable } from "@/lib/thirdweb";

interface SmartAccountTransactionButtonProps {
  transaction: PreparedTransaction;
  onTransactionSent?: (result: { transactionHash: string }) => void;
  onTransactionConfirmed?: () => void;
  onError?: (error: Error) => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

/**
 * Enhanced TransactionButton that properly handles smart account authorization
 * Fixes the 4100 error by implementing proper smart account flow
 */
const SmartAccountTransactionButton: React.FC<
  SmartAccountTransactionButtonProps
> = ({
  transaction,
  onTransactionSent,
  onTransactionConfirmed,
  onError,
  children,
  className = "",
  disabled = false,
}) => {
  const { toast } = useToast();
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const [isExecuting, setIsExecuting] = useState(false);

  const handleExecute = async () => {
    console.log("🚀 SmartAccountTransactionButton - Execute clicked:", {
      account: account?.address,
      wallet: wallet?.id,
      transaction: !!transaction,
      hasOnError: !!onError,
    });

    if (!account || !wallet || !transaction) {
      const error = new Error("No active account, wallet, or transaction");
      console.error("❌ Missing required data:", {
        account,
        wallet,
        transaction,
      });
      onError?.(error);
      return;
    }

    setIsExecuting(true);

    try {
      // Check if this is a smart wallet using our utility
      const smartAccountStatus = await checkSmartAccountStatus(wallet);

      console.log("Account type:", {
        accountAddress: account.address,
        walletAddress: wallet.getAccount()?.address,
        isSmartAccount: smartAccountStatus.isSmartAccount,
        walletId: wallet.id,
        smartAccountStatus,
      });

      // Prepare smart account if needed
      if (smartAccountStatus.isSmartAccount) {
        console.log("Preparing smart account...");
        const preparation = await prepareSmartAccount(wallet);
        if (!preparation.success) {
          throw new Error(
            preparation.error || "Failed to prepare smart account"
          );
        }
      }

      let txResult;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          console.log(`Executing transaction (attempt ${retryCount + 1})...`);

          // Send transaction
          txResult = await sendTransaction({
            transaction,
            account,
          });

          // If we get here, transaction was successful
          break;
        } catch (txError: any) {
          console.error(
            `Transaction attempt ${retryCount + 1} failed:`,
            txError
          );

          if (
            smartAccountStatus.isSmartAccount &&
            txError.code === 4100 &&
            retryCount < maxRetries
          ) {
            // Handle smart account authorization error with retry
            const recovery = await handleSmartAccountError(txError, wallet);

            if (recovery.shouldRetry) {
              console.log(
                "Retrying transaction after smart account recovery..."
              );
              retryCount++;
              continue;
            } else {
              throw new Error(recovery.errorMessage);
            }
          } else {
            // No more retries or different error
            throw txError;
          }
        }
      }

      if (!txResult) {
        throw new Error("Transaction failed after all retry attempts");
      }

      console.log("Transaction sent:", txResult);

      // Notify transaction sent
      onTransactionSent?.({ transactionHash: txResult.transactionHash });

      toast({
        title: "Transaction Submitted",
        description: `Transaction hash: ${txResult.transactionHash}`,
      });

      // Wait for confirmation
      const receipt = await waitForReceipt(txResult);
      console.log("Transaction confirmed:", receipt);

      // Notify transaction confirmed
      onTransactionConfirmed?.();

      toast({
        title: "Transaction Confirmed",
        description: "Your transaction has been confirmed on the blockchain",
      });
    } catch (error: any) {
      console.error("Transaction failed:", error);

      // Check for paymaster billing error
      if (
        error.message?.includes("Mainnets not enabled for this account") ||
        error.message?.includes("please enable billing")
      ) {
        const chainId = transaction.chain?.id;
        const isMainnet = chainId && !isGasSponsoringAvailable(chainId);

        onError?.(error);

        toast({
          title: "Paymaster Service Unavailable",
          description: isMainnet
            ? "Gas sponsoring is not available on mainnet without billing. Please switch to a testnet or enable billing at thirdweb.com/dashboard/settings/billing"
            : "Gas sponsoring service is temporarily unavailable. Please try again or switch networks.",
          variant: "destructive",
        });
        return;
      }

      // Use the utility function to get a user-friendly error message
      const errorMessage = getSmartAccountErrorMessage(
        error as SmartAccountError
      );
      onError?.(error);

      // Determine the appropriate title based on error type
      const title =
        error.code === 4100
          ? "Smart Account Authorization Required"
          : error.message?.includes("User rejected")
          ? "Transaction Cancelled"
          : "Transaction Failed";

      toast({
        title,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <Button
      onClick={handleExecute}
      disabled={!account || disabled || isExecuting}
      className={`${className} ${isExecuting ? "opacity-50" : ""}`}
    >
      {isExecuting ? (
        <div className="flex items-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
          Executing...
        </div>
      ) : (
        children
      )}
    </Button>
  );
};

export default SmartAccountTransactionButton;
