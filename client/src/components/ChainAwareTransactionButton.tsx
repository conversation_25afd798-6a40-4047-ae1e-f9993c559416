import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { 
  useActiveAccount, 
  useActiveWalletChain,
  useSendTransaction,
  useWaitForReceipt 
} from "thirdweb/react";
import {
  sendTransaction,
  waitForReceipt,
  define<PERSON>hain,
  type PreparedTransaction,
} from "thirdweb";
import { client, isGasSponsoringAvailable } from "@/lib/thirdweb";

interface ChainAwareTransactionButtonProps {
  transaction: PreparedTransaction;
  onTransactionSent?: (result: { transactionHash: string }) => void;
  onTransactionConfirmed?: () => void;
  onError?: (error: Error) => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const ChainAwareTransactionButton = ({
  transaction,
  onTransactionSent,
  onTransactionConfirmed,
  onError,
  children,
  className = "",
  disabled = false,
}: ChainAwareTransactionButtonProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  const activeChain = useActiveWalletChain();
  const [isExecuting, setIsExecuting] = useState(false);
  const [txHash, setTxHash] = useState<string | null>(null);

  // Wait for receipt if we have a transaction hash
  const receiptQuery = useWaitForReceipt({
    client,
    chain: activeChain || defineChain(********), // Default to Sepolia
    transactionHash: txHash as `0x${string}`,
  });

  const handleExecute = async () => {
    if (!activeAccount) {
      toast({
        title: "No Account",
        description: "Please connect your wallet first",
        variant: "destructive",
      });
      return;
    }

    if (!activeChain) {
      toast({
        title: "No Chain Selected",
        description: "Please select a network first",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);

    try {
      // Check if gas sponsoring is available for current chain
      const chainId = activeChain.id;
      const hasGasSponsoring = isGasSponsoringAvailable(chainId);
      
      console.log(`Executing transaction on chain ${chainId} (${activeChain.name})`);
      console.log(`Gas sponsoring available: ${hasGasSponsoring}`);

      // Execute the transaction
      const result = await sendTransaction({
        transaction,
        account: activeAccount,
      });

      setTxHash(result.transactionHash);
      onTransactionSent?.(result);

      toast({
        title: "Transaction Submitted",
        description: `Transaction hash: ${result.transactionHash}`,
      });

      // Wait for confirmation
      await waitForReceipt({
        client,
        chain: activeChain,
        transactionHash: result.transactionHash,
      });

      toast({
        title: "Transaction Confirmed",
        description: "Your transaction has been confirmed on the blockchain",
      });

      onTransactionConfirmed?.();

    } catch (error: any) {
      console.error("Transaction failed:", error);

      // Check for specific paymaster errors
      if (error.message?.includes("Mainnets not enabled for this account") || 
          error.message?.includes("please enable billing")) {
        const chainId = activeChain?.id;
        const isMainnet = chainId && !isGasSponsoringAvailable(chainId);
        
        onError?.(error);
        
        toast({
          title: "Paymaster Service Unavailable",
          description: isMainnet 
            ? `Gas sponsoring is not available on ${activeChain?.name || 'this network'} without billing. Please switch to a testnet or enable billing at thirdweb.com/dashboard/settings/billing`
            : "Gas sponsoring service is temporarily unavailable. Please try again or switch networks.",
          variant: "destructive",
        });
        return;
      }

      onError?.(error);

      toast({
        title: "Transaction Failed",
        description: error.message || "Failed to execute transaction",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // Show current chain info for debugging
  const chainInfo = activeChain ? {
    id: activeChain.id,
    name: activeChain.name,
    hasGasSponsoring: isGasSponsoringAvailable(activeChain.id)
  } : null;

  return (
    <div className="space-y-2">
      {/* Debug info */}
      {chainInfo && (
        <div className="text-xs text-muted-foreground p-2 bg-muted/20 rounded">
          Chain: {chainInfo.name} (ID: {chainInfo.id}) | Gas Sponsoring: {chainInfo.hasGasSponsoring ? "✅" : "❌"}
        </div>
      )}
      
      <Button
        onClick={handleExecute}
        disabled={disabled || isExecuting || !activeAccount || !activeChain}
        className={className}
      >
        {isExecuting ? (
          <>
            <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin mr-2" />
            Executing...
          </>
        ) : (
          children
        )}
      </Button>

      {/* Transaction status */}
      {txHash && (
        <div className="text-xs text-muted-foreground">
          {receiptQuery.isLoading ? (
            <div className="flex items-center">
              <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2" />
              Confirming transaction...
            </div>
          ) : receiptQuery.data ? (
            <div className="text-green-500">✅ Transaction confirmed</div>
          ) : receiptQuery.error ? (
            <div className="text-red-500">❌ Transaction failed</div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ChainAwareTransactionButton;
