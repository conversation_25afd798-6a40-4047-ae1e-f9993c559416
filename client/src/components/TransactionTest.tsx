import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useActiveAccount, useActiveWallet } from "thirdweb/react";
import { prepareTransaction, define<PERSON>hai<PERSON> } from "thirdweb";
import { client } from "@/lib/thirdweb";
import SmartAccountTransactionButton from "./SmartAccountTransactionButton";

/**
 * Test component to execute the transaction from the curl request
 */
const TransactionTest: React.FC = () => {
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const [txHash, setTxHash] = useState<string | null>(null);
  const [isExecuted, setIsExecuted] = useState(false);

  // Transaction data from the curl response
  const transactionData = {
    chainId: 80002, // Polygon Amoy
    to: "******************************************",
    value: "0xde0b6b3a7640000", // 1 AMOY in wei
    data: "0x"
  };

  // Prepare the transaction
  const preparedTransaction = React.useMemo(() => {
    if (!account) return null;

    try {
      const chain = defineChain(transactionData.chainId);
      
      return prepareTransaction({
        to: transactionData.to as `0x${string}`,
        value: BigInt(transactionData.value),
        data: transactionData.data as `0x${string}`,
        chain,
        client,
      });
    } catch (error) {
      console.error("Failed to prepare transaction:", error);
      return null;
    }
  }, [account]);

  const handleTransactionSent = (result: { transactionHash: string }) => {
    setTxHash(result.transactionHash);
    console.log("✅ Transaction sent:", result.transactionHash);
  };

  const handleTransactionConfirmed = () => {
    setIsExecuted(true);
    console.log("✅ Transaction confirmed!");
  };

  const handleError = (error: Error) => {
    console.error("❌ Transaction failed:", error);
  };

  // Convert wei to AMOY for display
  const amountInAMOY = Number(BigInt(transactionData.value)) / 1e18;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🧪 Transaction Test
          {isExecuted && <span className="text-green-500">✅</span>}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="space-y-2">
          <div className="text-sm">
            <strong>Wallet:</strong> {wallet ? `Connected (${wallet.id})` : "Not connected"}
          </div>
          <div className="text-sm">
            <strong>Account:</strong> {account ? `${account.address.slice(0, 6)}...${account.address.slice(-4)}` : "Not connected"}
          </div>
        </div>

        {/* Transaction Details */}
        <div className="space-y-2 p-3 bg-muted/20 rounded-lg">
          <div className="text-sm font-medium">Transaction Details:</div>
          <div className="text-xs space-y-1">
            <div><strong>To:</strong> {transactionData.to}</div>
            <div><strong>Amount:</strong> {amountInAMOY} AMOY</div>
            <div><strong>Chain:</strong> Polygon Amoy (80002)</div>
            <div><strong>Value (hex):</strong> {transactionData.value}</div>
          </div>
        </div>

        {/* Transaction Status */}
        {txHash && (
          <div className="space-y-2 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <div className="text-sm font-medium">Transaction Status:</div>
            <div className="text-xs">
              <div><strong>Hash:</strong> {txHash.slice(0, 10)}...{txHash.slice(-8)}</div>
              <div><strong>Status:</strong> {isExecuted ? "✅ Confirmed" : "⏳ Pending"}</div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`https://amoy.polygonscan.com/tx/${txHash}`, '_blank')}
              className="w-full"
            >
              View on Explorer
            </Button>
          </div>
        )}

        {/* Execute Button */}
        <div className="pt-2">
          {!account ? (
            <Button disabled className="w-full">
              Connect Wallet First
            </Button>
          ) : !preparedTransaction ? (
            <Button disabled className="w-full">
              Failed to Prepare Transaction
            </Button>
          ) : isExecuted ? (
            <Button disabled className="w-full bg-green-600">
              ✅ Transaction Executed
            </Button>
          ) : (
            <SmartAccountTransactionButton
              transaction={preparedTransaction}
              onTransactionSent={handleTransactionSent}
              onTransactionConfirmed={handleTransactionConfirmed}
              onError={handleError}
              className="w-full"
            >
              🚀 Execute Transaction (Send 1 AMOY)
            </SmartAccountTransactionButton>
          )}
        </div>

        {/* Debug Info */}
        <details className="text-xs">
          <summary className="cursor-pointer text-muted-foreground">Debug Info</summary>
          <pre className="mt-2 p-2 bg-muted/10 rounded text-xs overflow-auto">
            {JSON.stringify({
              transactionData,
              hasAccount: !!account,
              hasWallet: !!wallet,
              hasPreparedTx: !!preparedTransaction,
              txHash,
              isExecuted
            }, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  );
};

export default TransactionTest;
