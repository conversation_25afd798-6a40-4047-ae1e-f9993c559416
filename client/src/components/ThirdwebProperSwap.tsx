import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  useActiveAccount, 
  useBuyWithCryptoQuote,
} from "thirdweb/react";
import {
  sendTransaction,
  waitForR<PERSON>eipt,
  defineChain,
} from "thirdweb";
import { client } from "@/lib/thirdweb";
import { formatChainName } from "@/lib/chainConfig";

interface ThirdwebProperSwapProps {
  className?: string;
}

const ThirdwebProperSwap = ({ className = "" }: ThirdwebProperSwapProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  
  // Form state
  const [fromTokenAddress, setFromTokenAddress] = useState("******************************************"); // USDC
  const [toTokenAddress, setToTokenAddress] = useState("******************************************"); // USDT
  const [fromAmount, setFromAmount] = useState("1");
  const [chainId, setChainId] = useState(137); // Polygon
  const [maxSlippageBPS, setMaxSlippageBPS] = useState(50); // 0.5%
  
  // Execution state
  const [isExecuting, setIsExecuting] = useState(false);
  const [currentStep, setCurrentStep] = useState<"approval" | "swap" | null>(null);
  const [txHash, setTxHash] = useState<string | null>(null);

  // Get thirdweb swap quote - this is the CORRECT way
  const swapQuoteQuery = useBuyWithCryptoQuote(
    activeAccount && fromTokenAddress && toTokenAddress && fromAmount
      ? {
          client,
          fromAddress: activeAccount.address,
          fromChainId: chainId,
          fromTokenAddress,
          fromAmount,
          toChainId: chainId, // Same chain swap
          toTokenAddress,
          maxSlippageBPS,
        }
      : undefined
  );

  // Execute swap using thirdweb's CORRECT approach
  const handleSwapExecution = async () => {
    if (!activeAccount || !swapQuoteQuery.data) {
      toast({
        title: "Error",
        description: "No active account or swap quote available",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    
    try {
      const quote = swapQuoteQuery.data;
      
      // Step 1: Handle approval if required (AUTOMATIC with thirdweb)
      if (quote.approval) {
        setCurrentStep("approval");
        toast({
          title: "Approval Required",
          description: "Approving token spending...",
        });

        const approveTx = await sendTransaction({
          transaction: quote.approval,
          account: activeAccount,
        });

        await waitForReceipt({
          client,
          chain: defineChain(chainId),
          transactionHash: approveTx.transactionHash,
        });

        toast({
          title: "Approval Confirmed",
          description: "Token spending approved",
        });
      }

      // Step 2: Execute the swap (AUTOMATIC with thirdweb)
      setCurrentStep("swap");
      toast({
        title: "Executing Swap",
        description: "Swapping tokens...",
      });

      const swapTx = await sendTransaction({
        transaction: quote.transactionRequest,
        account: activeAccount,
      });

      setTxHash(swapTx.transactionHash);
      
      toast({
        title: "Swap Submitted",
        description: `Transaction hash: ${swapTx.transactionHash}`,
      });

      // Wait for confirmation
      await waitForReceipt({
        client,
        chain: defineChain(chainId),
        transactionHash: swapTx.transactionHash,
      });

      toast({
        title: "Swap Completed",
        description: "Successfully completed token swap",
      });

    } catch (error: any) {
      console.error("Swap failed:", error);
      toast({
        title: "Swap Failed",
        description: error.message || "Failed to execute swap",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
      setCurrentStep(null);
    }
  };

  const resetForm = () => {
    setTxHash(null);
    setCurrentStep(null);
    setIsExecuting(false);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Thirdweb Proper Swap Implementation</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Form */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="fromToken">From Token Address</Label>
            <Input
              id="fromToken"
              value={fromTokenAddress}
              onChange={(e) => setFromTokenAddress(e.target.value)}
              placeholder="0x..."
            />
          </div>
          <div>
            <Label htmlFor="toToken">To Token Address</Label>
            <Input
              id="toToken"
              value={toTokenAddress}
              onChange={(e) => setToTokenAddress(e.target.value)}
              placeholder="0x..."
            />
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              value={fromAmount}
              onChange={(e) => setFromAmount(e.target.value)}
              placeholder="1.0"
            />
          </div>
          <div>
            <Label htmlFor="chainId">Chain ID</Label>
            <Input
              id="chainId"
              type="number"
              value={chainId}
              onChange={(e) => setChainId(Number(e.target.value))}
              placeholder="137"
            />
          </div>
          <div>
            <Label htmlFor="slippage">Max Slippage (BPS)</Label>
            <Input
              id="slippage"
              type="number"
              value={maxSlippageBPS}
              onChange={(e) => setMaxSlippageBPS(Number(e.target.value))}
              placeholder="50"
            />
          </div>
        </div>

        {/* Quote Display */}
        {swapQuoteQuery.data && (
          <div className="p-3 bg-muted/20 rounded-lg">
            <div className="text-sm font-medium mb-2">Quote Details:</div>
            <div className="text-xs space-y-1">
              <div>From: {swapQuoteQuery.data.swapDetails.fromAmount} {swapQuoteQuery.data.swapDetails.fromToken.symbol}</div>
              <div>To: {swapQuoteQuery.data.swapDetails.toAmount} {swapQuoteQuery.data.swapDetails.toToken.symbol}</div>
              <div>Approval Required: {swapQuoteQuery.data.approval ? "Yes" : "No"}</div>
              <div>Estimated Gas: ${(swapQuoteQuery.data.swapDetails.estimated.gasCostUSDCents || 0) / 100}</div>
            </div>
          </div>
        )}

        {/* Status */}
        {(isExecuting || txHash) && (
          <div className="p-3 bg-muted/20 rounded-lg">
            <div className="text-sm font-medium mb-2">Status:</div>
            <div className="text-xs">
              {isExecuting ? (
                <div className="flex items-center">
                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2" />
                  {currentStep === "approval" ? "Approving tokens..." : "Executing swap..."}
                </div>
              ) : txHash ? (
                <div className="text-green-500">✓ Swap completed: {txHash}</div>
              ) : null}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {txHash ? (
            <Button onClick={resetForm} className="w-full">
              Reset
            </Button>
          ) : swapQuoteQuery.data ? (
            <Button
              onClick={handleSwapExecution}
              disabled={isExecuting || !activeAccount}
              className="w-full"
            >
              {isExecuting ? (
                <>
                  <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin mr-2" />
                  {currentStep === "approval" ? "Approving..." : "Swapping..."}
                </>
              ) : (
                "Execute Swap"
              )}
            </Button>
          ) : swapQuoteQuery.isLoading ? (
            <Button disabled className="w-full">
              <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin mr-2" />
              Getting Quote...
            </Button>
          ) : swapQuoteQuery.error ? (
            <Button disabled className="w-full">
              Quote Error: {swapQuoteQuery.error.message}
            </Button>
          ) : (
            <Button disabled className="w-full">
              Enter swap details
            </Button>
          )}
        </div>

        {/* Instructions */}
        <div className="text-xs text-muted-foreground p-3 bg-blue-500/10 rounded-lg">
          <div className="font-medium mb-1">This is the CORRECT thirdweb implementation:</div>
          <ul className="list-disc list-inside space-y-1">
            <li>Uses useBuyWithCryptoQuote hook</li>
            <li>Automatically handles approvals via quote.approval</li>
            <li>Uses quote.transactionRequest for swap execution</li>
            <li>No manual ERC20 approve() calls needed</li>
            <li>Follows thirdweb documentation exactly</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ThirdwebProperSwap;
